<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>邮箱配置测试</title>
    <link href="https://cdn.staticfile.net/remixicon/3.5.0/remixicon.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            padding: 20px;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .test-button {
            padding: 12px 24px;
            background-color: #007AFF;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
        }

        .test-button:hover {
            background-color: #0056CC;
        }

        /* 引入统一邮箱配置弹窗的样式 */
        .email-config-modal-unified {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .email-config-modal-content-unified {
            width: 90%;
            max-width: 700px;
            max-height: 90vh;
            background-color: white;
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .email-config-modal-header-unified {
            padding: 20px 24px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #f8f9fa;
        }

        .close-email-config-modal-unified {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            background: white;
            border: 1px solid #e0e0e0;
            cursor: pointer;
            font-size: 18px;
        }

        .email-type-selector {
            background-color: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
        }

        .email-type-tabs {
            display: flex;
            width: 100%;
        }

        .email-type-tab {
            flex: 1;
            padding: 16px 12px;
            border: none;
            background: transparent;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 6px;
            font-size: 13px;
            color: #666;
            border-bottom: 3px solid transparent;
        }

        .email-type-tab.active {
            color: #007AFF;
            background-color: white;
            border-bottom-color: #007AFF;
            font-weight: 600;
        }

        .email-type-tab i {
            font-size: 20px;
        }

        .email-config-modal-body-unified {
            padding: 24px;
            max-height: calc(90vh - 140px);
            overflow-y: auto;
        }

        .email-config-section {
            display: none;
        }

        .email-config-section.active {
            display: block;
        }

        .config-guide-card {
            background-color: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 24px;
        }

        .form-group {
            margin-bottom: 16px;
        }

        .form-group label {
            display: block;
            margin-bottom: 6px;
            font-weight: 500;
            color: #333;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            font-size: 14px;
        }

        .form-group input[readonly] {
            background-color: #f5f5f5;
            color: #666;
        }

        .form-row {
            display: flex;
            gap: 16px;
        }

        .form-group.half {
            flex: 1;
        }

        .auto-config-section {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
        }

        .auto-config-section h4 {
            margin: 0 0 12px 0;
            font-size: 14px;
            color: #666;
        }

        .form-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 24px;
        }

        .test-connection-btn, .save-config-btn {
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            cursor: pointer;
            border: none;
        }

        .test-connection-btn {
            background-color: #f8f9fa;
            border: 1px solid #e0e0e0;
            color: #333;
        }

        .save-config-btn {
            background-color: #007AFF;
            color: white;
        }

        .oauth-info-card {
            text-align: center;
            padding: 32px 24px;
            border: 2px solid #e0e0e0;
            border-radius: 16px;
        }

        .oauth-authorize-btn {
            padding: 12px 24px;
            background-color: #4285F4;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>邮箱配置功能测试</h1>
        <p>测试新的统一邮箱配置弹窗功能</p>
        
        <button class="test-button" onclick="openEmailConfig()">
            <i class="ri-mail-add-line"></i> 打开邮箱配置
        </button>
        
        <div id="test-results">
            <h3>测试结果：</h3>
            <div id="results-content"></div>
        </div>
    </div>

    <!-- 统一邮箱配置弹窗 -->
    <div class="email-config-modal-unified" id="email-config-modal-unified" style="display: none;">
        <div class="email-config-modal-content-unified">
            <div class="email-config-modal-header-unified">
                <h3>配置邮箱</h3>
                <button class="close-email-config-modal-unified" onclick="closeEmailConfig()">
                    <i class="ri-close-line"></i>
                </button>
            </div>
            
            <!-- 邮箱类型选择器 -->
            <div class="email-type-selector">
                <div class="email-type-tabs">
                    <button class="email-type-tab active" data-type="qq" onclick="switchTab('qq')">
                        <i class="ri-qq-fill"></i>
                        <span>QQ邮箱</span>
                    </button>
                    <button class="email-type-tab" data-type="163" onclick="switchTab('163')">
                        <i class="ri-mail-line"></i>
                        <span>163邮箱</span>
                    </button>
                    <button class="email-type-tab" data-type="gmail" onclick="switchTab('gmail')">
                        <i class="ri-google-fill"></i>
                        <span>Gmail</span>
                    </button>
                    <button class="email-type-tab" data-type="other" onclick="switchTab('other')">
                        <i class="ri-mail-settings-line"></i>
                        <span>其他邮箱</span>
                    </button>
                </div>
            </div>
            
            <div class="email-config-modal-body-unified">
                <!-- QQ邮箱配置区域 -->
                <div class="email-config-section active" id="qq-config-section">
                    <div class="config-guide-card">
                        <h4>📧 QQ邮箱配置指导</h4>
                        <ol>
                            <li>登录QQ邮箱，进入设置 → 账户</li>
                            <li>开启"IMAP/SMTP服务"</li>
                            <li>获取授权码作为密码使用</li>
                        </ol>
                    </div>
                    
                    <div class="form-group">
                        <label>邮箱地址</label>
                        <input type="email" placeholder="例如: <EMAIL>">
                    </div>
                    <div class="form-group">
                        <label>显示名称</label>
                        <input type="text" placeholder="例如: 您的姓名">
                    </div>
                    <div class="form-group">
                        <label>授权码</label>
                        <input type="password" placeholder="请输入QQ邮箱授权码">
                    </div>
                    
                    <div class="auto-config-section">
                        <h4>自动配置参数</h4>
                        <div class="form-row">
                            <div class="form-group half">
                                <label>SMTP服务器</label>
                                <input type="text" value="smtp.qq.com" readonly>
                            </div>
                            <div class="form-group half">
                                <label>SMTP端口</label>
                                <input type="text" value="465 (SSL)" readonly>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-actions">
                        <button class="test-connection-btn">测试连接</button>
                        <button class="save-config-btn">保存配置</button>
                    </div>
                </div>

                <!-- Gmail配置区域 -->
                <div class="email-config-section" id="gmail-config-section">
                    <div class="oauth-info-card">
                        <h4>🔐 Gmail授权配置</h4>
                        <p>通过Google OAuth安全授权访问Gmail邮箱</p>
                        <button class="oauth-authorize-btn">
                            <i class="ri-google-fill"></i> 授权Gmail账号
                        </button>
                    </div>
                </div>

                <!-- 其他配置区域可以类似添加 -->
            </div>
        </div>
    </div>

    <script>
        function openEmailConfig() {
            document.getElementById('email-config-modal-unified').style.display = 'flex';
            logResult('打开邮箱配置弹窗');
        }

        function closeEmailConfig() {
            document.getElementById('email-config-modal-unified').style.display = 'none';
            logResult('关闭邮箱配置弹窗');
        }

        function switchTab(type) {
            // 更新标签状态
            document.querySelectorAll('.email-type-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelector(`[data-type="${type}"]`).classList.add('active');

            // 切换配置区域
            document.querySelectorAll('.email-config-section').forEach(section => {
                section.classList.remove('active');
            });
            document.getElementById(`${type}-config-section`).classList.add('active');

            logResult(`切换到${type}邮箱配置`);
        }

        function logResult(message) {
            const resultsContent = document.getElementById('results-content');
            const timestamp = new Date().toLocaleTimeString();
            resultsContent.innerHTML += `<p>[${timestamp}] ${message}</p>`;
        }
    </script>
</body>
</html>
